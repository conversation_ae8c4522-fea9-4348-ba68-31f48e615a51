<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Models\ExerciseModel;
use App\Models\PositionsGroupModel;
use App\Models\PositionsModel;
use App\Models\AppxApplicationDetailsModel;
use App\Models\AppxApplicationProfileModel;
use App\Models\AppxApplicationRatingModel;
use App\Models\RateItemsModel;

/**
 * ReportsForm37Controller
 * 
 * Controller for Form 3.7 Profiling reports with navigation flow:
 * Exercise -> Position Groups -> Positions -> Form 3.7 Report
 */
class ReportsForm37Controller extends Controller
{
    protected $exerciseModel;
    protected $positionGroupModel;
    protected $positionModel;
    protected $applicationModel;
    protected $profileModel;
    protected $ratingModel;
    protected $rateItemsModel;

    public function __construct()
    {
        helper(['url', 'form']);
        $this->exerciseModel = new ExerciseModel();
        $this->positionGroupModel = new PositionsGroupModel();
        $this->positionModel = new PositionsModel();
        $this->applicationModel = new AppxApplicationDetailsModel();
        $this->profileModel = new AppxApplicationProfileModel();
        $this->ratingModel = new AppxApplicationRatingModel();
        $this->rateItemsModel = new RateItemsModel();
    }

    /**
     * [GET] Form 3.7 Report - List Position Groups for Exercise
     * URI: /reports/form37/{exerciseId}
     */
    public function form37($exerciseId)
    {
        // Get exercise data
        $exercise = $this->exerciseModel->find($exerciseId);
        if (!$exercise) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Exercise not found');
        }

        // Get position groups with position counts for this exercise
        $positionGroups = $this->positionGroupModel->getPositionGroupsWithCountByExerciseId($exerciseId);

        $data = [
            'title' => 'Form 3.7 Profiling Report - ' . $exercise['exercise_name'],
            'menu' => 'reports',
            'exercise' => $exercise,
            'position_groups' => $positionGroups
        ];

        return view('application_reports/appx_reports_form37_groups', $data);
    }

    /**
     * [GET] List Positions in Position Group
     * URI: /reports/form37/groups/{groupId}
     */
    public function groups($groupId)
    {
        // Get position group data
        $positionGroup = $this->positionGroupModel->find($groupId);
        if (!$positionGroup) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Position group not found');
        }

        // Get exercise data
        $exercise = $this->exerciseModel->find($positionGroup['exercise_id']);
        if (!$exercise) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Exercise not found');
        }

        // Get positions with application counts for this group
        $positions = $this->positionModel->getPositionsByGroupIdWithApplicationCount($groupId);

        $data = [
            'title' => 'Form 3.7 Profiling - Positions in ' . $positionGroup['group_name'],
            'menu' => 'reports',
            'exercise' => $exercise,
            'position_group' => $positionGroup,
            'positions' => $positions
        ];

        return view('application_reports/appx_reports_form37_positions', $data);
    }

    /**
     * [GET] Form 3.7 Profiling Report for Position
     * URI: /reports/form37/positions/{positionId}
     */
    public function positions($positionId)
    {
        // Get position data with related information
        $position = $this->positionModel->select('
                positions.*,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                dakoii_org.org_name
            ')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->find($positionId);

        if (!$position) {
            return redirect()->to('reports/exercises')
                            ->with('error', 'Position not found');
        }

        // Get applications for this position
        $applications = $this->applicationModel->where('position_id', $positionId)
            ->orderBy('last_name', 'ASC')
            ->findAll();

        // Get exercise ID for profile lookup
        $exerciseId = $position['exercise_id'];

        // Enhance applications with profile data and ratings
        $enhancedApplications = [];
        foreach ($applications as $application) {
            // Get profile data for this applicant and exercise using applicant_id
            $profile = $this->profileModel->getProfiledApplicantDetails($application['applicant_id'], $exerciseId);

            // Get rating data for this application using application ID
            $ratings = $this->ratingModel->getRatingsWithJustificationsByApplicationId($application['id']);
            $ratingSummary = $this->ratingModel->getRatingSummary($application['id']);

            // Combine application with profile and rating data
            $enhancedApplication = [
                'id' => $application['id'], // Keep application ID for ratings
                'applicant_id' => $application['applicant_id'], // Keep applicant ID for reference
                'profile' => $profile,
                'ratings' => $ratings,
                'rating_summary' => $ratingSummary,
                'profile_status' => $profile['remarks'] ?? 'Not Profiled'
            ];

            $enhancedApplications[] = $enhancedApplication;
        }

        // Group applications by status for better organization
        $groupedApplications = [
            'shortlisted' => [],
            'eliminated' => [],
            'withdrawn' => [],
            'others' => []
        ];

        foreach ($enhancedApplications as $application) {
            $status = strtolower($application['profile_status'] ?? 'others');
            if (strpos($status, 'shortlist') !== false) {
                $groupedApplications['shortlisted'][] = $application;
            } elseif (strpos($status, 'eliminat') !== false) {
                $groupedApplications['eliminated'][] = $application;
            } elseif (strpos($status, 'withdraw') !== false) {
                $groupedApplications['withdrawn'][] = $application;
            } else {
                $groupedApplications['others'][] = $application;
            }
        }

        // Combine in priority order: shortlisted, eliminated, withdrawn, others
        $sortedApplications = array_merge(
            $groupedApplications['shortlisted'],
            $groupedApplications['eliminated'],
            $groupedApplications['withdrawn'],
            $groupedApplications['others']
        );

        // Get all rate items for the rating table
        $rateItems = $this->rateItemsModel->getAllItems();

        $data = [
            'title' => 'Form 3.7 Profiling Report - ' . $position['designation'],
            'menu' => 'reports',
            'position' => $position,
            'applications' => $sortedApplications,
            'total_applications' => count($applications),
            'rate_items' => $rateItems
        ];

        return view('application_reports/appx_reports_form37_report', $data);
    }

    /**
     * [POST] Export Form 3.7 Profiling Report to PDF
     * URI: /reports/form37/positions/export
     */
    public function exportForm37()
    {
        // Get and validate parameters
        $positionId = $this->request->getPost('position_id');
        if (!$positionId) {
            $this->response->setContentType('application/json');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Position ID is required'
            ]);
        }

        try {
            // Get position data with related information
            $position = $this->positionModel->select('
                    positions.*,
                    positions_groups.group_name,
                    positions_groups.exercise_id,
                    exercises.exercise_name,
                    dakoii_org.org_name
                ')
                ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
                ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
                ->find($positionId);

            if (!$position) {
                $this->response->setContentType('application/json');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Position not found'
                ]);
            }

            // Get applications for this position
            $applications = $this->applicationModel->where('position_id', $positionId)
                ->orderBy('last_name', 'ASC')
                ->findAll();

            // Get exercise ID for profile lookup
            $exerciseId = $position['exercise_id'];

            // Enhance applications with profile data and ratings
            $enhancedApplications = [];
            foreach ($applications as $application) {
                // Get profile data for this applicant and exercise using applicant_id
                $profile = $this->profileModel->getProfiledApplicantDetails($application['applicant_id'], $exerciseId);

                // Get rating data for this application using application ID
                $ratings = $this->ratingModel->getRatingsWithJustificationsByApplicationId($application['id']);
                $ratingSummary = $this->ratingModel->getRatingSummary($application['id']);

                // Combine application with profile and rating data
                $enhancedApplication = [
                    'id' => $application['id'], // Keep application ID for ratings
                    'applicant_id' => $application['applicant_id'], // Keep applicant ID for reference
                    'profile' => $profile,
                    'ratings' => $ratings,
                    'rating_summary' => $ratingSummary,
                    'profile_status' => $profile['remarks'] ?? 'Not Profiled'
                ];

                $enhancedApplications[] = $enhancedApplication;
            }

            // Get all rate items for the rating table
            $rateItems = $this->rateItemsModel->getAllItems();

            // Generate and output PDF directly to browser
            $this->generateForm37PDF($position, $enhancedApplications, $rateItems);

        } catch (\Exception $e) {
            log_message('error', 'Form 3.7 PDF Export Error: ' . $e->getMessage());
            $this->response->setContentType('application/json');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to generate PDF: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Generate Form 3.7 Profiling PDF
     *
     * @param array $position Position data
     * @param array $applications Applications data with profiles and ratings
     * @param array $rateItems Rate items for scoring table
     */
    private function generateForm37PDF($position, $applications, $rateItems)
    {
        try {
            // Create TCPDF with custom footer (Landscape orientation)
            $pdf = new class('L', 'mm', 'A4', true, 'UTF-8', false) extends \TCPDF {
                public function Footer() {
                    $this->SetY(-18);
                    $footerY = $this->GetY();
                    $footerHeight = 15;
                    $footerWidth = $this->getPageWidth() - 20; // Account for left and right margins (10mm each)

                    // Draw PNG flag colored sections
                    $this->SetFillColor(240, 15, 0); // Red
                    $this->Rect(10, $footerY, $footerWidth / 3, $footerHeight, 'F');

                    $this->SetFillColor(0, 0, 0); // Black
                    $this->Rect(10 + ($footerWidth / 3), $footerY, $footerWidth / 3, $footerHeight, 'F');

                    $this->SetFillColor(255, 194, 15); // Gold
                    $this->Rect(10 + (2 * $footerWidth / 3), $footerY, $footerWidth / 3, $footerHeight, 'F');

                    // Add content
                    $this->SetFont('helvetica', '', 8);
                    $this->SetY($footerY + 1);

                    // Row 1
                    $this->SetTextColor(255, 255, 255);
                    $this->SetX(12);
                    $this->Cell($footerWidth / 3, 3, 'Generated by DERS System v1.0', 0, 0, 'L');

                    $this->SetX(10 + ($footerWidth / 3));
                    $this->Cell($footerWidth / 3, 3, 'Dakoii Echad Recruitment & Selection System', 0, 0, 'C');

                    $this->SetTextColor(0, 0, 0);
                    $this->SetX(10 + (2 * $footerWidth / 3));
                    $this->Cell($footerWidth / 3, 3, 'Generated on: ' . date('M d, Y H:i') . ' | Page ' . $this->getAliasNumPage() . ' of ' . $this->getAliasNbPages(), 0, 0, 'C');

                    // Row 2
                    $this->SetY($this->GetY() + 3);
                    $this->SetTextColor(255, 255, 255);
                    $this->SetX(12);
                    $this->Cell($footerWidth / 3, 3, 'AI-Powered', 0, 0, 'L');

                    $this->SetX(10 + ($footerWidth / 3));
                    $this->Cell($footerWidth / 3, 3, 'Developed by Dakoii Systems & Echad Consultancy Services', 0, 0, 'C');

                    // Row 3
                    $this->SetY($this->GetY() + 3);
                    $this->SetX(10);
                    $this->Cell($footerWidth, 3, 'ders.dakoiims.com', 0, 0, 'C');

                    $this->SetTextColor(0, 0, 0);
                }
            };

            // Configure PDF
            $pdf->SetCreator('DERS System');
            $pdf->SetTitle('Form 3.7 Profiling Report - ' . $position['designation']);
            $pdf->SetMargins(10, 20, 10);
            $pdf->SetAutoPageBreak(true, 25);
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(true);

            // Add page
            $pdf->AddPage();

            // Add logo
            $logoPath = FCPATH . 'public/assets/system_img/system-logo.png';
            if (file_exists($logoPath)) {
                $pdf->Image($logoPath, ($pdf->getPageWidth() - 20) / 2, $pdf->GetY(), 20, 20);
                $pdf->Ln(25);
            }

            // Add title
            $pdf->SetFont('helvetica', 'B', 16);
            $pdf->Cell(0, 10, 'APPLICANT PROFILE REPORT', 0, 1, 'C');
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(0, 8, 'FORM RS 3.7', 0, 1, 'R');
            $pdf->Ln(5);

            // Add content
            $this->addForm37Content($pdf, $position, $applications, $rateItems);

            // Generate filename and output directly to browser
            $filename = 'form_37_profiling_' . $position['position_reference'] . '_' . date('Y-m-d_H-i-s') . '.pdf';
            $pdf->Output($filename, 'D');

        } catch (\Exception $e) {
            log_message('error', 'PDF Generation Error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Add Form 3.7 content to PDF
     *
     * @param object $pdf TCPDF instance
     * @param array $position Position data
     * @param array $applications Applications data with profiles and ratings
     * @param array $rateItems Rate items for scoring table
     */
    private function addForm37Content($pdf, $position, $applications, $rateItems)
    {
        // Position Information
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, 'Position Information', 0, 1, 'L');
        $pdf->SetFont('helvetica', '', 10);

        // Use two-column layout
        $leftColWidth = ($pdf->getPageWidth() - 20) / 2 - 5;
        $rightColWidth = ($pdf->getPageWidth() - 20) / 2 - 5;

        // Left column
        $startY = $pdf->GetY();
        $pdf->Cell(50, 6, 'Position Reference:', 0, 0, 'L');
        $pdf->Cell($leftColWidth - 50, 6, $position['position_reference'], 0, 1, 'L');

        $pdf->Cell(50, 6, 'Designation:', 0, 0, 'L');
        $pdf->Cell($leftColWidth - 50, 6, $position['designation'], 0, 1, 'L');

        $pdf->Cell(50, 6, 'Classification:', 0, 0, 'L');
        $pdf->Cell($leftColWidth - 50, 6, $position['classification'], 0, 1, 'L');

        // Right column
        $pdf->SetXY(10 + $leftColWidth + 10, $startY);

        $pdf->Cell(50, 6, 'Exercise:', 0, 0, 'L');
        $pdf->Cell($rightColWidth - 50, 6, $position['exercise_name'], 0, 1, 'L');

        $pdf->SetX(10 + $leftColWidth + 10);
        $pdf->Cell(50, 6, 'Position Group:', 0, 0, 'L');
        $pdf->Cell($rightColWidth - 50, 6, $position['group_name'], 0, 1, 'L');

        $pdf->SetX(10 + $leftColWidth + 10);
        $pdf->Cell(50, 6, 'Organization:', 0, 0, 'L');
        $pdf->Cell($rightColWidth - 50, 6, $position['org_name'], 0, 1, 'L');

        // Reset to full width and add spacing
        $pdf->SetX(10);
        $pdf->Ln(8);

        // Applications Summary
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, 'Applications Summary', 0, 1, 'L');
        $pdf->SetFont('helvetica', '', 10);

        $pdf->Cell(50, 6, 'Total Applications:', 0, 0, 'L');
        $pdf->Cell(0, 6, count($applications), 0, 1, 'L');

        $pdf->Ln(5);

        // Process applications in groups of 3 per page for better readability
        $applicantsPerPage = 3;
        $totalPages = ceil(count($applications) / $applicantsPerPage);

        for ($page = 1; $page <= $totalPages; $page++) {
            if ($page > 1) {
                $pdf->AddPage();
            }

            $startIndex = ($page - 1) * $applicantsPerPage;
            $pageApplicants = array_slice($applications, $startIndex, $applicantsPerPage);

            // Page header with spacing
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(0, 8, 'Comparative Analysis - Page ' . $page . ' of ' . $totalPages, 0, 1, 'L');
            $pdf->Ln(3);

            // Generate comparative table for this page with improved spacing
            $this->generateComparativeTable($pdf, $position, $pageApplicants, $startIndex);

            // Add rating table for this page with proper spacing
            if (!empty($rateItems)) {
                $this->generateRatingTable($pdf, $pageApplicants, $rateItems, $startIndex);
            }
        }
    }

    /**
     * Generate comparative table for Form 3.7
     *
     * @param object $pdf TCPDF instance
     * @param array $position Position data
     * @param array $pageApplicants Applicants for this page
     * @param int $startIndex Starting index for applicant numbering
     */
    private function generateComparativeTable($pdf, $position, $pageApplicants, $startIndex)
    {
        $pdf->SetFont('helvetica', '', 8);

        // Calculate table width to fit full content width
        $contentWidth = $pdf->getPageWidth() - 20;
        $col1Width = 65;  // Position Specification - slightly wider
        $remainingWidth = $contentWidth - $col1Width;
        $colWidth = $remainingWidth / count($pageApplicants); // Person Specification columns

        // Table header with better formatting
        $pdf->SetFillColor(240, 240, 240);
        $pdf->SetFont('helvetica', 'B', 8);

        // Position specification header
        $pdf->MultiCell($col1Width, 10, 'POSITION SPECIFICATION', 1, 'C', true, 0, '', '', true, 0, false, true, 10, 'M');

        // Person specification headers
        for ($i = 0; $i < count($pageApplicants); $i++) {
            $headerText = "PERSON SPECIFICATION\nApplicant " . ($startIndex + $i + 1);
            $pdf->MultiCell($colWidth, 10, $headerText, 1, 'C', true, 0, '', '', true, 0, false, true, 10, 'M');
        }
        $pdf->Ln();

        // Personal Information Section
        $this->addTableSection($pdf, 'Personal Information', $position, $pageApplicants, $col1Width, $colWidth, function($position, $applicant) {
            return "Name: " . ($applicant['profile']['full_name'] ?? 'N/A') . "\n" .
                   "Sex: " . ($applicant['profile']['sex'] ?? 'N/A') . "\n" .
                   "Age: " . ($applicant['profile']['bdate_age'] ?? 'N/A') . "\n" .
                   "Place of Origin: " . ($applicant['profile']['place_origin'] ?? 'N/A') . "\n" .
                   "Current Position: " . ($applicant['profile']['current_position'] ?? 'N/A') . "\n" .
                   "Current Employer: " . ($applicant['profile']['current_employer'] ?? 'N/A') . "\n" .
                   "Address: " . ($applicant['profile']['address_location'] ?? 'N/A');
        }, 'Personal details');

        // Qualifications Section
        $this->addTableSection($pdf, 'Qualifications', $position, $pageApplicants, $col1Width, $colWidth, function($position, $applicant) {
            return $applicant['profile']['qualification_text'] ?? 'Not provided';
        }, $position['qualifications'] ?? 'Not specified');

        // Other Training Courses Section
        $this->addTableSection($pdf, 'Other Training Courses', $position, $pageApplicants, $col1Width, $colWidth, function($position, $applicant) {
            return $applicant['profile']['other_trainings'] ?? 'Not provided';
        }, 'N/A');

        // Skills & Competencies Section
        $this->addTableSection($pdf, 'Skills & Competencies', $position, $pageApplicants, $col1Width, $colWidth, function($position, $applicant) {
            return $applicant['profile']['skills_competencies'] ?? 'Not provided';
        }, $position['skills_competencies'] ?? 'Not specified');

        // Job Experiences Section
        $this->addTableSection($pdf, 'Job Experiences', $position, $pageApplicants, $col1Width, $colWidth, function($position, $applicant) {
            return $applicant['profile']['job_experiences'] ?? 'Not provided';
        }, $position['job_experiences'] ?? 'Not specified');

        // Knowledge Section
        $this->addTableSection($pdf, 'Knowledge', $position, $pageApplicants, $col1Width, $colWidth, function($position, $applicant) {
            return $applicant['profile']['knowledge'] ?? 'Not provided';
        }, $position['knowledge'] ?? 'Not specified');

        // Remarks Section
        $this->addTableSection($pdf, 'Remarks', $position, $pageApplicants, $col1Width, $colWidth, function($position, $applicant) {
            return $applicant['profile']['remarks'] ?? 'Not provided';
        }, $position['remarks'] ?? 'Not specified');
    }

    /**
     * Add table section with STRICT overflow prevention and complete table structure
     */
    private function addTableSection($pdf, $sectionTitle, $position, $pageApplicants, $col1Width, $colWidth, $personDataCallback, $positionData)
    {
        // Collect all person data for this row
        $personDataArray = [];
        foreach ($pageApplicants as $applicant) {
            $personDataArray[] = $personDataCallback($position, $applicant);
        }

        // STRICT overflow detection - check if ANY content would overflow
        $contentSplits = $this->calculateContentSplits($pdf, $positionData, $personDataArray, $col1Width, $colWidth);

        if (count($contentSplits) > 1 || $this->wouldContentOverflow($pdf, $positionData, $personDataArray, $col1Width, $colWidth)) {
            // Content needs to be split across multiple pages OR would cause overflow
            $this->addTableSectionWithSplitContent($pdf, $sectionTitle, $pageApplicants, $col1Width, $colWidth, $contentSplits);
        } else {
            // Content definitely fits on one page without ANY overflow
            $this->addTableSectionSinglePage($pdf, $sectionTitle, $pageApplicants, $col1Width, $colWidth, $positionData, $personDataArray);
        }
    }

    /**
     * Calculate bottom margin with 10% rule: no table rows in bottom 10% of content space
     */
    private function calculateBottomMargin($pdf)
    {
        $pageHeight = $pdf->getPageHeight();
        $topMargin = 20; // Standard top margin
        $footerSpace = 25; // Space for footer
        $contentSpaceHeight = $pageHeight - $topMargin - $footerSpace;

        // Return footer space + 10% of content space height
        return $footerSpace + ($contentSpaceHeight * 0.1);
    }

    /**
     * Check if content would cause ANY overflow (even a single character)
     */
    private function wouldContentOverflow($pdf, $positionData, $personDataArray, $col1Width, $colWidth)
    {
        $currentY = $pdf->GetY();
        $pageHeight = $pdf->getPageHeight();
        $bottomMargin = $this->calculateBottomMargin($pdf);

        $headerHeight = 10;
        $lineHeight = 4.5;
        $minPadding = 8;

        // Calculate exact space needed
        $positionLines = $pdf->getNumLines($positionData, $col1Width - 6);
        $maxPersonLines = 0;
        foreach ($personDataArray as $personData) {
            $personLines = $pdf->getNumLines($personData, $colWidth - 6);
            $maxPersonLines = max($maxPersonLines, $personLines);
        }

        $maxLines = max($positionLines, $maxPersonLines);
        $requiredRowHeight = $maxLines * $lineHeight + $minPadding;
        $totalRequired = $headerHeight + $requiredRowHeight + 2; // Small buffer

        $availableSpace = $pageHeight - $currentY - $bottomMargin;

        // Return true if content would overflow by even 1 pixel
        return $totalRequired > $availableSpace;
    }

    /**
     * Add table section with strict overflow detection - prevents ANY content overflow
     */
    private function addTableSectionSinglePage($pdf, $sectionTitle, $pageApplicants, $col1Width, $colWidth, $positionData, $personDataArray)
    {
        // Calculate exact content requirements for each cell
        $positionLines = $pdf->getNumLines($positionData, $col1Width - 6);
        $personLines = [];
        foreach ($personDataArray as $personData) {
            $personLines[] = $pdf->getNumLines($personData, $colWidth - 6);
        }
        $maxLines = max($positionLines, max($personLines));

        $headerHeight = 10;
        $lineHeight = 4.5;
        $minPadding = 8;

        // Calculate EXACT space requirements with 10% bottom margin rule
        $currentY = $pdf->GetY();
        $pageHeight = $pdf->getPageHeight();
        $bottomMargin = $this->calculateBottomMargin($pdf);
        $availableSpace = $pageHeight - $currentY - $bottomMargin;

        // Calculate minimum required row height for ALL content to fit
        $minRequiredRowHeight = $maxLines * $lineHeight + $minPadding;
        $totalSectionHeight = $headerHeight + $minRequiredRowHeight;

        // STRICT CHECK: If ANY part would overflow, move entire section to new page
        if ($totalSectionHeight > $availableSpace) {
            $pdf->AddPage();
            $currentY = $pdf->GetY();
            $availableSpace = $pageHeight - $currentY - $bottomMargin;

            // Recalculate for new page
            $maxUsableHeight = $availableSpace - $headerHeight - 5;
            $rowHeight = min($minRequiredRowHeight, $maxUsableHeight);
        } else {
            // Content fits on current page
            $rowHeight = $minRequiredRowHeight;
        }

        // Ensure minimum readable height
        $rowHeight = max($rowHeight, 20);

        // Add complete header and content with all columns
        $this->addCompleteTableHeader($pdf, $sectionTitle, $pageApplicants, $col1Width, $colWidth);
        $startX = $pdf->GetX();
        $contentStartY = $pdf->GetY();
        $this->addCompleteTableRow($pdf, $startX, $contentStartY, $col1Width, $colWidth, $rowHeight, $positionData, $personDataArray, count($pageApplicants));
        $pdf->SetXY($startX, $contentStartY + $rowHeight);
    }

    /**
     * Add table section with content split across multiple pages
     * Ensures every page has complete table headers with all columns
     */
    private function addTableSectionWithSplitContent($pdf, $sectionTitle, $pageApplicants, $col1Width, $colWidth, $contentSplits)
    {
        foreach ($contentSplits as $splitIndex => $splitData) {
            // Add new page if not the first split
            if ($splitIndex > 0) {
                $pdf->AddPage();
            }

            // ALWAYS add complete table header with all columns on every page
            $this->addCompleteTableHeader($pdf, $sectionTitle, $pageApplicants, $col1Width, $colWidth);

            // Calculate optimal row height for this split to maximize page usage
            $maxLines = 1;
            $positionLines = $pdf->getNumLines($splitData['positionData'], $col1Width - 6);
            $maxLines = max($maxLines, $positionLines);

            foreach ($splitData['personDataArray'] as $personData) {
                $personLines = $pdf->getNumLines($personData, $colWidth - 6);
                $maxLines = max($maxLines, $personLines);
            }

            // Calculate available space for content with 10% bottom margin rule
            $currentY = $pdf->GetY();
            $pageHeight = $pdf->getPageHeight();
            $bottomMargin = $this->calculateBottomMargin($pdf);
            $availableHeight = $pageHeight - $currentY - $bottomMargin;

            // Use available space more efficiently
            $lineHeight = 4.5;
            $minPadding = 8;
            $calculatedHeight = $maxLines * $lineHeight + $minPadding;

            // Don't exceed available space, but use as much as possible
            $maxPossibleHeight = $availableHeight - 5; // Small buffer
            $rowHeight = min($calculatedHeight, $maxPossibleHeight);

            // Ensure minimum readable height
            $minRowHeight = 15;
            $rowHeight = max($minRowHeight, $rowHeight);

            // Add complete content row with all columns (even if some are empty)
            $startX = $pdf->GetX();
            $contentStartY = $pdf->GetY();
            $totalApplicants = isset($splitData['totalApplicants']) ? $splitData['totalApplicants'] : count($pageApplicants);
            $this->addCompleteTableRow($pdf, $startX, $contentStartY, $col1Width, $colWidth, $rowHeight,
                                     $splitData['positionData'], $splitData['personDataArray'], $totalApplicants);
            $pdf->SetXY($startX, $contentStartY + $rowHeight);
        }
    }

    /**
     * Add complete table header with all columns (ensures consistency across pages)
     */
    private function addCompleteTableHeader($pdf, $sectionTitle, $pageApplicants, $col1Width, $colWidth)
    {
        $pdf->SetFillColor(250, 250, 250);
        $pdf->SetFont('helvetica', 'B', 9);
        $headerHeight = 10;

        $startX = $pdf->GetX();
        $startY = $pdf->GetY();

        // Always add the section title column
        $pdf->Cell($col1Width, $headerHeight, $sectionTitle, 1, 0, 'L', true);

        // Always add ALL applicant columns, even if some might be empty in content
        foreach ($pageApplicants as $index => $applicant) {
            $applicantNumber = $index + 1;
            $pdf->Cell($colWidth, $headerHeight, 'Applicant ' . $applicantNumber, 1, 0, 'C', true);
        }
        $pdf->Ln();
    }

    /**
     * Add table header (legacy method for backward compatibility)
     */
    private function addTableHeader($pdf, $sectionTitle, $pageApplicants, $col1Width, $colWidth)
    {
        $this->addCompleteTableHeader($pdf, $sectionTitle, $pageApplicants, $col1Width, $colWidth);
    }

    /**
     * Add a complete table row ensuring ALL columns are present with proper N/A handling
     */
    private function addCompleteTableRow($pdf, $startX, $startY, $col1Width, $colWidth, $rowHeight, $positionData, $personDataArray, $totalApplicants)
    {
        // Set consistent background for all cells
        $pdf->SetFillColor(255, 255, 255);

        // Position specification column (first column) - always present
        $pdf->SetXY($startX, $startY);
        $cleanPositionData = empty(trim($positionData)) ? 'N/A' : $positionData;
        $this->addConsistentCell($pdf, $col1Width, $rowHeight, $cleanPositionData, 'L');

        // Person specification columns - ensure ALL applicant columns are present
        $currentX = $startX + $col1Width;
        for ($i = 0; $i < $totalApplicants; $i++) {
            $pdf->SetXY($currentX, $startY);

            // Use actual data if available, otherwise use N/A
            $personData = isset($personDataArray[$i]) ? $personDataArray[$i] : '';

            // STRICT N/A handling - show N/A for any empty content
            if (empty(trim($personData))) {
                $personData = 'N/A';
            }

            $this->addConsistentCell($pdf, $colWidth, $rowHeight, $personData, 'L');
            $currentX += $colWidth;
        }
    }

    /**
     * Add a complete table row with consistent cell heights (legacy method for backward compatibility)
     */
    private function addTableRow($pdf, $startX, $startY, $col1Width, $colWidth, $rowHeight, $positionData, $personDataArray)
    {
        // Calculate total applicants from personDataArray length
        $totalApplicants = count($personDataArray);
        $this->addCompleteTableRow($pdf, $startX, $startY, $col1Width, $colWidth, $rowHeight, $positionData, $personDataArray, $totalApplicants);
    }

    /**
     * Calculate content splits with STRICT overflow prevention
     */
    private function calculateContentSplits($pdf, $positionData, $personDataArray, $col1Width, $colWidth)
    {
        $pageHeight = $pdf->getPageHeight();
        $currentY = $pdf->GetY();
        $bottomMargin = $this->calculateBottomMargin($pdf);

        $headerHeight = 10;
        $lineHeight = 4.5;
        $minRowPadding = 8;

        // STRICT calculation - ensure NO overflow occurs
        $availableHeight = $pageHeight - $currentY - $bottomMargin - $headerHeight - $minRowPadding - 5; // Extra buffer
        $maxLinesCurrentPage = floor($availableHeight / $lineHeight);

        // For subsequent pages, use full page height with strict margins
        $fullPageAvailableHeight = $pageHeight - $bottomMargin - $headerHeight - $minRowPadding - 25; // Top margin + buffer
        $maxLinesPerFullPage = floor($fullPageAvailableHeight / $lineHeight);

        // STRICT minimum requirements - if can't fit meaningful content, force new page
        if ($maxLinesCurrentPage < 3) {
            $maxLinesCurrentPage = 0; // Force new page
        }

        if ($maxLinesPerFullPage < 8) {
            $maxLinesPerFullPage = 8; // Absolute minimum
        }

        // Calculate EXACT lines needed for each piece of content
        $positionLines = $pdf->getNumLines($positionData, $col1Width - 6);
        $personLinesArray = [];
        $maxPersonLines = 0;

        foreach ($personDataArray as $personData) {
            $personLines = $pdf->getNumLines($personData, $colWidth - 6);
            $personLinesArray[] = $personLines;
            $maxPersonLines = max($maxPersonLines, $personLines);
        }

        $totalLinesNeeded = max($positionLines, $maxPersonLines);

        // STRICT CHECK: If content fits completely on current page without ANY overflow
        if ($totalLinesNeeded <= $maxLinesCurrentPage && $maxLinesCurrentPage > 0) {
            return [[
                'positionData' => $positionData,
                'personDataArray' => $personDataArray,
                'totalApplicants' => count($personDataArray)
            ]];
        }

        // Content needs splitting - ensure complete table structure on each page
        $contentSplits = [];
        $remainingPositionData = $positionData;
        $remainingPersonData = $personDataArray;
        $totalApplicants = count($personDataArray);
        $isFirstPage = ($maxLinesCurrentPage > 0);

        while (!empty($remainingPositionData) || !empty(array_filter($remainingPersonData))) {
            $currentPageMaxLines = $isFirstPage ? $maxLinesCurrentPage : $maxLinesPerFullPage;

            // If first page can't fit anything, skip to new page
            if ($currentPageMaxLines <= 0) {
                $isFirstPage = false;
                $currentPageMaxLines = $maxLinesPerFullPage;
            }

            // Split position data for current page
            $positionSplit = $this->splitTextForPage($pdf, $remainingPositionData, $col1Width - 6, $currentPageMaxLines);

            // Split person data for current page - MAINTAIN ALL COLUMNS
            $personSplitArray = [];
            for ($i = 0; $i < $totalApplicants; $i++) {
                if (isset($remainingPersonData[$i]) && !empty($remainingPersonData[$i])) {
                    $personSplit = $this->splitTextForPage($pdf, $remainingPersonData[$i], $colWidth - 6, $currentPageMaxLines);
                    $personSplitArray[] = $personSplit['currentPage'];
                    $remainingPersonData[$i] = $personSplit['remaining'];
                } else {
                    // Empty columns will show N/A
                    $personSplitArray[] = '';
                    if (isset($remainingPersonData[$i])) {
                        $remainingPersonData[$i] = '';
                    }
                }
            }

            $contentSplits[] = [
                'positionData' => $positionSplit['currentPage'],
                'personDataArray' => $personSplitArray,
                'totalApplicants' => $totalApplicants
            ];

            $remainingPositionData = $positionSplit['remaining'];
            $isFirstPage = false;

            // Break if no more content remains
            if (empty($remainingPositionData) && empty(array_filter($remainingPersonData))) {
                break;
            }
        }

        return $contentSplits;
    }

    /**
     * Split text for a specific page, maximizing content utilization
     */
    private function splitTextForPage($pdf, $text, $width, $maxLines)
    {
        if (empty($text)) {
            return ['currentPage' => '', 'remaining' => ''];
        }

        $totalLines = $pdf->getNumLines($text, $width);

        // If text fits entirely on current page
        if ($totalLines <= $maxLines) {
            return ['currentPage' => $text, 'remaining' => ''];
        }

        // Split text to maximize current page usage
        $words = explode(' ', $text);
        $currentPageText = '';
        $currentLines = 0;
        $wordIndex = 0;

        // Build text for current page, maximizing line usage
        foreach ($words as $index => $word) {
            $testText = empty($currentPageText) ? $word : $currentPageText . ' ' . $word;
            $testLines = $pdf->getNumLines($testText, $width);

            if ($testLines <= $maxLines) {
                $currentPageText = $testText;
                $currentLines = $testLines;
                $wordIndex = $index + 1;
            } else {
                // Adding this word would exceed page limit
                break;
            }
        }

        // Build remaining text from unused words
        $remainingWords = array_slice($words, $wordIndex);
        $remainingText = implode(' ', $remainingWords);

        return [
            'currentPage' => $currentPageText,
            'remaining' => $remainingText
        ];
    }

    /**
     * Split text into chunks that fit within specified line limits (legacy method for compatibility)
     */
    private function splitTextIntoChunks($pdf, $text, $width, $maxLinesPerChunk)
    {
        if (empty($text)) {
            return [''];
        }

        $chunks = [];
        $remainingText = $text;

        while (!empty($remainingText)) {
            $split = $this->splitTextForPage($pdf, $remainingText, $width, $maxLinesPerChunk);
            if (!empty($split['currentPage'])) {
                $chunks[] = $split['currentPage'];
            }
            $remainingText = $split['remaining'];

            // Prevent infinite loop
            if ($remainingText === $text) {
                $chunks[] = $remainingText;
                break;
            }
            $text = $remainingText;
        }

        return empty($chunks) ? [''] : $chunks;
    }

    /**
     * Add a single cell with consistent height and proper N/A handling
     */
    private function addConsistentCell($pdf, $width, $height, $text, $align)
    {
        $startX = $pdf->GetX();
        $startY = $pdf->GetY();

        // Clean and prepare text with strict N/A handling
        $text = trim($text);
        if (empty($text)) {
            $text = 'N/A';
        }

        // Draw cell border first
        $pdf->Rect($startX, $startY, $width, $height, 'D');

        // Calculate available space for text
        $textWidth = $width - 4; // 2mm padding on each side
        $textHeight = $height - 3; // 1.5mm padding top and bottom

        // Special handling for N/A - center it and make it more visible
        if ($text === 'N/A') {
            $pdf->SetFont('helvetica', 'I', 8); // Italic for N/A
            $pdf->SetXY($startX + 2, $startY + ($height / 2) - 2);
            $pdf->Cell($textWidth, 4, $text, 0, 0, 'C');
            $pdf->SetFont('helvetica', '', 8); // Reset font
        } else {
            // Calculate optimal line height based on available space
            $totalLines = $pdf->getNumLines($text, $textWidth);
            $optimalLineHeight = $totalLines > 0 ? min(4.5, $textHeight / $totalLines) : 4.5;
            $optimalLineHeight = max($optimalLineHeight, 3); // Minimum readable line height

            // Position text to utilize full cell height
            $pdf->SetXY($startX + 2, $startY + 1.5);
            $pdf->MultiCell($textWidth, $optimalLineHeight, $text, 0, $align, false, 1, '', '', true, 0, false, true, $textHeight, 'T');
        }
    }



    /**
     * Add MultiCell with border using consistent height (Legacy method - simplified for table consistency)
     */
    private function addMultiCellWithBorder($pdf, $width, $height, $text, $border, $align)
    {
        // Use the new consistent cell method for better table alignment
        $this->addConsistentCell($pdf, $width, $height, $text, $align);
    }

    /**
     * Generate rating criteria table with proper page break handling
     */
    private function generateRatingTable($pdf, $pageApplicants, $rateItems, $startIndex)
    {
        if (empty($rateItems)) {
            return;
        }

        $pdf->Ln(10);

        // Check if we have space for the rating table header with 10% bottom margin rule
        $currentY = $pdf->GetY();
        $pageHeight = $pdf->getPageHeight();
        $bottomMargin = $this->calculateBottomMargin($pdf);
        $availableSpace = $pageHeight - $currentY - $bottomMargin;
        $headerHeight = 20; // Title + table header

        if ($availableSpace < $headerHeight + 20) {
            $pdf->AddPage();
        }

        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, 'Rating Criteria and Scores', 0, 1, 'L');
        $pdf->SetFont('helvetica', '', 8);

        // Calculate table width
        $contentWidth = $pdf->getPageWidth() - 20;
        $col1Width = 60;  // Criteria
        $colOutWidth = 20; // Out of
        $remainingWidth = $contentWidth - $col1Width - $colOutWidth;
        $colWidth = $remainingWidth / count($pageApplicants); // Applicant columns

        // Table header
        $this->addRatingTableHeader($pdf, $pageApplicants, $startIndex, $col1Width, $colWidth, $colOutWidth);

        // Rating rows with page break handling
        $totalAchieved = array_fill(0, count($pageApplicants), 0);
        $totalMax = 0;
        $rowHeight = 8;

        foreach ($rateItems as $rateItem) {
            // Check if row fits on current page
            $currentY = $pdf->GetY();
            $availableSpace = $pageHeight - $currentY - $bottomMargin;

            if ($availableSpace < $rowHeight + 10) {
                $pdf->AddPage();
                // Re-add header on new page
                $this->addRatingTableHeader($pdf, $pageApplicants, $startIndex, $col1Width, $colWidth, $colOutWidth);
            }

            // Criteria label with text wrapping
            $criteriaText = $rateItem['item_label'];
            $lines = $pdf->getNumLines($criteriaText, $col1Width - 4);
            $actualRowHeight = max($rowHeight, $lines * 4 + 2);

            $startY = $pdf->GetY();
            $startX = $pdf->GetX();

            // Draw criteria cell
            $pdf->MultiCell($col1Width, $actualRowHeight, $criteriaText, 1, 'L', false, 0, '', '', true, 0, false, true, $actualRowHeight, 'M');

            $maxScore = 0;
            $currentX = $startX + $col1Width;

            // Draw score cells
            foreach ($pageApplicants as $index => $applicant) {
                $score = 0;
                $maxScoreForItem = 0;

                // Find rating for this applicant and rate item
                if (!empty($applicant['ratings'])) {
                    foreach ($applicant['ratings'] as $rating) {
                        if ($rating['rate_item_id'] == $rateItem['id']) {
                            $score = $rating['score_achieved'];
                            $maxScoreForItem = $rating['score_max'];
                            break;
                        }
                    }
                }

                $totalAchieved[$index] += $score;
                if ($maxScoreForItem > $maxScore) {
                    $maxScore = $maxScoreForItem;
                }

                $pdf->SetXY($currentX, $startY);
                $pdf->Cell($colWidth, $actualRowHeight, $score, 1, 0, 'C');
                $currentX += $colWidth;
            }

            // Out of column
            $pdf->SetXY($currentX, $startY);
            $pdf->Cell($colOutWidth, $actualRowHeight, $maxScore, 1, 0, 'C');
            $totalMax += $maxScore;

            // Move to next row
            $pdf->SetXY($startX, $startY + $actualRowHeight);
        }

        // Total row
        $currentY = $pdf->GetY();
        $availableSpace = $pageHeight - $currentY - $bottomMargin;

        if ($availableSpace < $rowHeight + 5) {
            $pdf->AddPage();
            $this->addRatingTableHeader($pdf, $pageApplicants, $startIndex, $col1Width, $colWidth, $colOutWidth);
        }

        $pdf->SetFillColor(220, 220, 220);
        $pdf->SetFont('helvetica', 'B', 8);
        $pdf->Cell($col1Width, $rowHeight, 'Total', 1, 0, 'L', true);
        foreach ($totalAchieved as $total) {
            $pdf->Cell($colWidth, $rowHeight, $total, 1, 0, 'C', true);
        }
        $pdf->Cell($colOutWidth, $rowHeight, $totalMax, 1, 1, 'C', true);
    }

    /**
     * Add rating table header
     */
    private function addRatingTableHeader($pdf, $pageApplicants, $startIndex, $col1Width, $colWidth, $colOutWidth)
    {
        $pdf->SetFillColor(240, 240, 240);
        $pdf->SetFont('helvetica', 'B', 8);
        $pdf->Cell($col1Width, 8, 'Criteria', 1, 0, 'C', true);
        for ($i = 0; $i < count($pageApplicants); $i++) {
            $pdf->Cell($colWidth, 8, 'Applicant ' . ($startIndex + $i + 1), 1, 0, 'C', true);
        }
        $pdf->Cell($colOutWidth, 8, 'Out of', 1, 1, 'C', true);
    }
}
